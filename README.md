# 📜 vest-list

vest-list is a CLI tool for manipulating presale whitelists.

It takes a snapshot created with [vest-snapshot](https://gitlab.tenset.io/vest/vest-snapshot), applies modifications, and generates a whitelist compatible with `vest-evm` protocol.

### Instalation

1. Clone the repository:

```sh
<NAME_EMAIL>:vest/vest-list.git
```

2. Install dependencies:

```sh
cd vest-list
npm install
```

### Usage

1. Create a folder with the project name in `/data` directory.
2. Create a config.yaml file in `/data/<project_name>` with the structure

- **oneAllocationSize** - Size of one allocation
- **delayInSeconds**: - Cliff for vesting after TGE
- **tgePercent** - How many percent of tokens user is able to claim at TGE
- **tgeDenominator** - Denominator for tgePercent
- **vestingPeriods** - How many parts will the token vesting be divided into
- **vestingPeriodDurationInSeconds** - What will be the time interval between picking up each part of the vesting.
  example `config.yaml`:

```yaml
oneAllocationSize: "10833333333333333333333"
delayInSeconds: 0
tgePercent: 0
tgeDenominator: 100
vestingPeriods: 12
vestingPeriodDurationInSeconds: "30 days"
```
4. Paste the `mapped-allocations.csv` file generated by [vest-snapshot](https://gitlab.tenset.io/vest/vest-snapshot) in `/data/<project_name>/`

5. (Optional) Paste the additional .csv file you want to concatenate with the allocations in `/data/<project_name>/`
4. Run script

```sh
npm run start generate <project-name> [options]
```

Optionally, use the -cf or --concat-file flag to specify a filename to concatenate with the allocations file. The additional file should also have the same headers as the main allocations file.

Examples:
```sh
npm run generate my-awesome-project
```

```sh
npm run generate my-awesome-project -cf additional-allocations
```

### Created files
- **whitelist.csv** - A file containing all the settings according to the presale configuration. Ready to generate merkle tree using [vest-tree](https://gitlab.tenset.io/vest/vest-tree)