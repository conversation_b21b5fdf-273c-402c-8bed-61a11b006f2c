{"name": "vest-list", "version": "1.0.0", "description": "vest-list is a CLI tool for manipulating presale whitelists.", "bin": "dist/index.js", "scripts": {"start": "tsx src/index.ts", "build": "tsc"}, "author": "Tenset <<EMAIL>> (https://tenset.io/)", "license": "UNLICENSED", "dependencies": {"@commander-js/extra-typings": "11.1.0", "chrono-node": "2.7.4", "fs-extra": "11.2.0", "js-yaml": "4.1.0", "papaparse": "5.4.1", "parse-duration": "1.1.0", "zod": "3.22.4"}, "devDependencies": {"@types/fs-extra": "11.0.4", "@types/js-yaml": "4.0.9", "@types/papaparse": "5.3.14", "tsx": "4.19.0", "typescript": "5.3.3"}}