import { Option, program } from '@commander-js/extra-typings'
import fs from 'fs-extra'
import path from 'path'
import { loadConfig } from './utils/config'
import { readCsv, validateHeaders, writeToCsv } from './utils/csv'

const allocationHeaders = ['address', 'tier', 'allocations']

// Define a type for your CSV rows
interface CsvRow {
  address: string
  tier: number
  allocations: number
}

export interface WhitelistRow {
  address: string
  tier: number
  price: string
  allocation: string
  claimbackPeriod: string
  tgeNumerator: number
  tgeDenominator: number
  cliffDuration: number
  cliffNumerator: number
  cliffDenominator: number
  vestingPeriodCount: number
  vestingPeriodDuration: number
  tradeable: 1 | 2
}

program
  .command('generate')
  .argument(
    '<project-name>',
    'Name of the project for which you want take snapshot'
  )
  .addOption(
    new Option(
      '-cf, --concat-file <config>',
      'Specify filename to concat with allocations file'
    )
  )
  .action(async (projectName, options) => {
    const config = await loadConfig(projectName)

    console.log(`Called generate with config`, config)

    // Read allocations file
    const absoluteAllocationsPath = path.resolve(
      `data/${projectName}/mapped-allocations.csv`
    )
    if (!(await fs.exists(absoluteAllocationsPath)))
      throw new Error(
        `mapped-allocations.csv file at ${absoluteAllocationsPath} seems to not exist`
      )

    const allocations = await readCsv<CsvRow>(absoluteAllocationsPath)
    validateHeaders(
      absoluteAllocationsPath,
      Object.keys(allocations[0]),
      allocationHeaders
    )

    // Read concat file if specified
    if (options.concatFile) {
      const absoluteAllocationsPath = path.resolve(
        `data/${projectName}/${options.concatFile}.csv`
      )
      const concatAllocationFile = await readCsv<CsvRow>(
        absoluteAllocationsPath
      )
      validateHeaders(
        absoluteAllocationsPath,
        Object.keys(concatAllocationFile[0]),
        allocationHeaders
      )
      console.log(
        `Concatting ${concatAllocationFile.length} rows from ${options.concatFile}.csv`
      )
      allocations.push(...concatAllocationFile)
    }

    const whitelist: WhitelistRow[] = []

    for (const row of allocations) {
      const { address, tier, allocations } = row

      const price = getValueWithCorrectDecimals(
        config.price.toString(),
        config.priceDecimals
      )
      const allocationSize = getValueWithCorrectDecimals(
        config.allocation.toString(),
        config.allocationDecimals
      )

      const _allocations = allocations.toString().split('.')
      const decimals = _allocations[1] ? _allocations[1].length : 0

      let userAllocationSize =
        BigInt(+allocations * 10 ** decimals) * allocationSize
      userAllocationSize = userAllocationSize / BigInt(10 ** decimals)

      whitelist.push({
        address: address.toLowerCase(),
        tier: tier,
        price: price.toString(),
        allocation: userAllocationSize.toString(),
        claimbackPeriod: config.tiers
          .find((t) => t.tier === tier)!
          .claimbackPeriod.toString(),
        tgeNumerator: config.tgeNumerator,
        tgeDenominator: config.tgeDenominator,
        cliffDuration: config.cliffDuration,
        cliffNumerator: config.cliffNumerator,
        cliffDenominator: config.cliffDenominator,
        vestingPeriodCount: config.vestingPeriodCount,
        vestingPeriodDuration: Number(config.vestingPeriodDuration),
        tradeable: config.tiers.find((t) => t.tier === tier)!.tradeable,
      })
    }

    writeToCsv(whitelist, `data/${projectName}/whitelist.csv`)
  })
;(async () => program.parseAsync())()

const getValueWithCorrectDecimals = (value: string, decimals: number) => {
  const values = value.toString().split('.')
  const decimalsLength = values[1] ? values[1].length : 0

  const allowedDecimals = decimalsLength ? values[1].slice(0, decimals) : ''
  const newValue = values[0] + (allowedDecimals ? '.' + allowedDecimals : '')

  const valueWithCorrectDecimals =
    BigInt(newValue.replace('.', '')) *
    BigInt(10 ** (decimals - allowedDecimals.length))

  return valueWithCorrectDecimals
}
