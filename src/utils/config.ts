import fs from 'fs-extra'
import yaml from 'js-yaml'
import parseDuration from 'parse-duration'
import path from 'path'

export interface ProjectConfig {
  price: number
  priceDecimals: number
  allocation: string
  allocationDecimals: number
  tgeNumerator: number
  tgeDenominator: number
  cliffDuration: number
  cliffNumerator: number
  cliffDenominator: number
  vestingPeriodCount: number
  vestingPeriodDuration: number | string
  tiers: {
    tier: number
    claimbackPeriod: number
    tradeable: 1 | 2
  }[]
}

export function validateConfig(config: ProjectConfig): void {
  const schema: ProjectConfig = {
    price: 0,
    priceDecimals: 0,
    allocation: '0',
    allocationDecimals: 0,
    tgeNumerator: 0,
    tgeDenominator: 0,
    cliffDuration: 0,
    cliffNumerator: 0,
    cliffDenominator: 0,
    vestingPeriodCount: 0,
    vestingPeriodDuration: 0,
    tiers: [],
  }

  // if (typeof config.delayInSeconds === 'string') {
  //   config.delayInSeconds = parseDuration(config.delayInSeconds, "second")!
  // }

  if (typeof config.vestingPeriodDuration === 'string') {
    config.vestingPeriodDuration = parseDuration(
      config.vestingPeriodDuration,
      'second'
    )!
  }

  // for each key in schema, check if it exists in config and if it's of the same type
  for (const key in schema) {
    if (
      typeof config[key as keyof ProjectConfig] !==
      typeof schema[key as keyof ProjectConfig]
    ) {
      throw new Error(
        `Config validation failed: ${key} is not of type ${typeof schema[
          key as keyof ProjectConfig
        ]}`
      )
    }
  }

  // check if all tiers have required fields
  for (const tier of config.tiers) {
    const tierSchema = {
      tier: 0,
      claimbackPeriod: 0,
      tradeable: 1,
    }

    for (const key in tierSchema) {
      if (
        typeof tier[key as keyof typeof tierSchema] !==
        typeof tierSchema[key as keyof typeof tierSchema]
      ) {
        throw new Error(
          `Config validation failed: ${key} is not of type ${typeof tierSchema[
            key as keyof typeof tierSchema
          ]}`
        )
      }
    }
  }
}

export async function loadConfig(configFile: string): Promise<ProjectConfig> {
  const absoluteConfigPath = path.resolve(`data/${configFile}/config.yaml`)
  if (!(await fs.exists(absoluteConfigPath)))
    throw new Error(`Config file at ${absoluteConfigPath} seems to not exist`)

  const fileContents = fs.readFileSync(absoluteConfigPath, 'utf8')
  const config = yaml.load(fileContents) as ProjectConfig

  // check if all required fields are present
  validateConfig(config)

  return config
}
